<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <?php echo $html->charset(); ?>
  <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7" />
  <title><?php echo $title_for_layout; ?></title>
  <?php
    echo $html->css('/framework/themes/stylesheets/reset');
    echo $html->css('/webadmin-ui/stylesheets/screen');
    echo $html->css('/webadmin-ui/stylesheets/forms');
    echo $html->css('/webadmin-ui/stylesheets/controls');
    echo $html->css('/webadmin-ui/stylesheets/compatibility.css');
  ?>
</head>
<body class="<?php echo $this->params['controller']; ?>">
  <div id="container">
    <div id="header" class="clearfix">
      <div id="spinner">
        <?php //echo $html->image('spinner.gif'); ?>
      </div>
      <h1>Bon Voyage WebAdmin</h1>
      <?php echo $this->element('webadmin_user_info'); ?>
      <?php echo $this->element('webadmin_menu'); ?>
    </div>
    <div id="wrapper" class="clearfix">
      <div id="content">
      <?php echo $content_for_layout; ?>
      </div>
    </div>
    <div id="debug"></div>
    <div id="footer">&copy; <?php echo date('Y'); ?></div>
  </div>
  <?php echo $cakeDebug; ?>
  <?php
      echo $javascript->link(array(
        // was previously using '/bower_components/prototype.js/dist/prototype.min.js'
        // I copied the contents to /framework/lib/prototype_1.7_rc2 (version specified in the code)
        // and made the modification (Array.from = Array.from || $A)
        '/framework/lib/prototype_1.7_rc2',
        '/framework/lib/effects',
        '/framework/lib/dragdrop',
        '/framework/lib/slider',
        '/framework/src/ui/ui',
        '/framework/src/ui/modal',
        '/framework/src/ui/autocompleter',
        '/framework/src/ui/mapeditor',
        '/framework/src/ui/tagpicker',
        '/framework/src/ui/tabset',
        '/webadmin-ui/javascripts/webadmin-compatibility',
        '/webadmin-ui/javascripts/lib/daydream',
        '/webadmin-ui/javascripts/lib/xhtml_purifier',
        '/webadmin-ui/javascripts/lib/wysihat',
        '/webadmin-ui/javascripts/webadmin-editors-wysihat',
        'webadmin/application'
      ));

    echo $scripts_for_layout;
  ?>
</body>
</html>
