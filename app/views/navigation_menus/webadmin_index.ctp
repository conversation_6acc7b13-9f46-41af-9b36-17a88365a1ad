<div id="farWideIndex" class="index">
  <div class="title clearfix">
    <h2><?php __('Far & Wide');?></h2>
    <?php echo $this->element('webadmin_actions'); ?>
  </div>

  <?php $webAdmin->sessionFlash(); ?>



  <?php if (!empty($navigationMenus)): ?>
    <?php if (!empty($breadcrumbs)): ?>
      <?php echo $this->element('webadmin_breadcrumb'); ?>
    <?php endif; ?>

    <div class="table">
      <table cellpadding="0" cellspacing="0" id="farWideTable">
        <thead>
          <tr>
            <th class="reorder"></th>
            <th>Name</th>
            <th>URL</th>
            <th>Menu Type</th>
            <th>Published</th>
            <th>Created</th>
            <th>Modified</th>
            <th class="actions"><?php __('Actions'); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php
          // Render menu items hierarchically
          function renderMenuItems($items, $html, $level = 0) {
              $output = '';
              $i = 0;

              foreach ($items as $item) {
                  $class = '';
                  if ($i++ % 2 == 0) {
                      $class = ' class="altrow"';
                  }

                  $indent = str_repeat('&nbsp;&nbsp;&nbsp;', $level);
                  $published = $item['NavigationMenu']['published'] ? 'Yes' : 'No';

                  $output .= '<tr' . $class . ' id="row-' . $item['NavigationMenu']['id'] . '">';
                  $output .= '<td class="reorder">';
                  $output .= '<span class="drag">Drag</span>';
                  $output .= '</td>';
                  $output .= '<td>' . $indent . h($item['NavigationMenu']['name']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['url']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['menu_type']) . '</td>';
                  $output .= '<td>' . $published . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['created']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['modified']) . '</td>';
                  $output .= '<td class="actions">';
                  $output .= $html->link(__('Edit', true), array('action' => 'webadmin_edit', $item['NavigationMenu']['id']));
                  $output .= ' ';
                  $output .= $html->link(__('Delete', true), array('action' => 'webadmin_delete', $item['NavigationMenu']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $item['NavigationMenu']['id']));
                  $output .= ' ';
                  $publishText = $item['NavigationMenu']['published'] ? 'Unpublish' : 'Publish';
                  $output .= $html->link($publishText, array('action' => 'webadmin_toggle_field', 'published', $item['NavigationMenu']['id']));
                  $output .= '</td>';
                  $output .= '</tr>';

                  // Render children
                  if (!empty($item['children'])) {
                      $output .= renderMenuItems($item['children'], $html, $level + 1);
                  }
              }

              return $output;
          }

          echo renderMenuItems($navigationMenus, $html, 0);
          ?>
        </tbody>
      </table>
    </div>
    <?php echo $javascript->codeBlock("
      console.log('[Far & Wide Debug] Starting initialization...');
      try {
        var sortableTable = new Webadmin.SortableTable('farWideTable', {
          controller: 'navigation_menus',
          model: 'NavigationMenu'
        });
        console.log('[Far & Wide Debug] SortableTable created:', sortableTable);

        // Check if the underlying UI.SortableTable was created
        if (sortableTable.sortable) {
          console.log('[Far & Wide Debug] UI.SortableTable found:', sortableTable.sortable);

          // Add manual event listeners to test if drag elements are responsive
          var dragElements = $('farWideTable').select('span.drag');
          console.log('[Far & Wide Debug] Found drag elements:', dragElements.length);

          dragElements.each(function(element, index) {
            element.observe('mousedown', function(event) {
              console.log('[Far & Wide Debug] Mousedown on drag element', index, event);

              // Try to manually trigger the sortable's onDrag method
              var row = element.up('tr');
              if (row && sortableTable.dragEvent) {
                console.log('[Far & Wide Debug] Manually calling dragEvent for row:', row.identify());
                try {
                  sortableTable.dragEvent(row);
                  console.log('[Far & Wide Debug] dragEvent called successfully');
                } catch(e) {
                  console.error('[Far & Wide Debug] Error calling dragEvent:', e);
                }
              }
            });
            element.observe('click', function(event) {
              console.log('[Far & Wide Debug] Click on drag element', index, event);
            });
          });
        } else {
          console.error('[Far & Wide Debug] UI.SortableTable not created!');
        }

        // Test the AJAX endpoint manually
        setTimeout(function() {
          console.log('[Far & Wide Debug] Testing AJAX endpoint manually...');
          new Ajax.Request('/webadmin/navigation_menus/save_field/2.json', {
            method: 'post',
            postBody: 'data[NavigationMenu][order]=0',
            onSuccess: function(response) {
              console.log('[Far & Wide Debug] Manual AJAX Success:', response.responseText);
            },
            onFailure: function(response) {
              console.error('[Far & Wide Debug] Manual AJAX Failed:', response.status, response.responseText);
            }
          });
        }, 3000);
      } catch(e) {
        console.error('[Far & Wide Debug] Error creating SortableTable:', e);
      }
    ", array('inline' => false)); ?>

  <?php else: ?>
    <p>No Far & Wide items found.</p>
  <?php endif; ?>
</div>
