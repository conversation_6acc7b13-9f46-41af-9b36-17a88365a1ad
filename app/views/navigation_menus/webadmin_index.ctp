<div id="farWideIndex" class="index">
  <div class="title clearfix">
    <h2><?php __('Far & Wide');?></h2>
    <?php echo $this->element('webadmin_actions'); ?>
  </div>

  <?php $webAdmin->sessionFlash(); ?>



  <?php if (!empty($navigationMenus)): ?>
    <?php if (!empty($breadcrumbs)): ?>
      <?php echo $this->element('webadmin_breadcrumb'); ?>
    <?php endif; ?>

    <div class="table">
      <table cellpadding="0" cellspacing="0" id="farWideTable">
        <thead>
          <tr>
            <th class="reorder"></th>
            <th>Name</th>
            <th>URL</th>
            <th>Menu Type</th>
            <th>Published</th>
            <th>Created</th>
            <th>Modified</th>
            <th class="actions"><?php __('Actions'); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php
          // Render menu items hierarchically
          function renderMenuItems($items, $html, $level = 0) {
              $output = '';
              $i = 0;

              foreach ($items as $item) {
                  $class = '';
                  if ($i++ % 2 == 0) {
                      $class = ' class="altrow"';
                  }

                  $indent = str_repeat('&nbsp;&nbsp;&nbsp;', $level);
                  $published = $item['NavigationMenu']['published'] ? 'Yes' : 'No';

                  $output .= '<tr' . $class . ' id="row-' . $item['NavigationMenu']['id'] . '">';
                  $output .= '<td class="reorder">';
                  $output .= '<span class="drag">Drag</span>';
                  $output .= '</td>';
                  $output .= '<td>' . $indent . h($item['NavigationMenu']['name']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['url']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['menu_type']) . '</td>';
                  $output .= '<td>' . $published . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['created']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['modified']) . '</td>';
                  $output .= '<td class="actions">';
                  $output .= $html->link(__('Edit', true), array('action' => 'webadmin_edit', $item['NavigationMenu']['id']));
                  $output .= ' ';
                  $output .= $html->link(__('Delete', true), array('action' => 'webadmin_delete', $item['NavigationMenu']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $item['NavigationMenu']['id']));
                  $output .= ' ';
                  $publishText = $item['NavigationMenu']['published'] ? 'Unpublish' : 'Publish';
                  $output .= $html->link($publishText, array('action' => 'webadmin_toggle_field', 'published', $item['NavigationMenu']['id']));
                  $output .= '</td>';
                  $output .= '</tr>';

                  // Render children
                  if (!empty($item['children'])) {
                      $output .= renderMenuItems($item['children'], $html, $level + 1);
                  }
              }

              return $output;
          }

          echo renderMenuItems($navigationMenus, $html, 0);
          ?>
        </tbody>
      </table>
    </div>
    <?php echo $javascript->codeBlock("
      console.log('[Far & Wide Debug] Starting initialization...');

      // Create SortableTable
      var sortableTable = new Webadmin.SortableTable('farWideTable', {
        controller: 'navigation_menus',
        model: 'NavigationMenu'
      });

      // Override both onDrag and onEnd methods to add debugging
      var originalOnDrag = sortableTable.onDrag.bind(sortableTable);
      sortableTable.onDrag = function(row) {
        console.log('[Far & Wide Debug] Drag started for row:', row.identify());
        return originalOnDrag(row);
      };

      var originalOnEnd = sortableTable.onEnd.bind(sortableTable);
      sortableTable.onEnd = function(row) {
        console.log('[Far & Wide Debug] Drag ended for row:', row.identify());

        var value = this.sortable.serialize().indexOf(row.identify());
        console.log('[Far & Wide Debug] New position:', value, 'Old position:', this.index);

        if(value != this.index) {
          var replacements = Object.extend(this.options, {
            value : value,
            id : row.identify().toRecordId()
          });

          var url = this.url.evaluate(replacements);
          var postBody = this.postBody.evaluate(replacements);

          console.log('[Far & Wide Debug] Making AJAX request to:', url);
          console.log('[Far & Wide Debug] POST data:', postBody);

          new Ajax.Request(url, {
            method : 'post',
            postBody : postBody,
            onSuccess : function(response) {
              console.log('[Far & Wide Debug] AJAX Success:', response.responseText);
            },
            onFailure : function(response) {
              console.error('[Far & Wide Debug] AJAX Failed:', response.status, response.responseText);
            },
            onException : function(response, exception) {
              console.error('[Far & Wide Debug] AJAX Exception:', exception);
            }
          });
        } else {
          console.log('[Far & Wide Debug] No position change, skipping AJAX');
        }
        this.index = null;
      };

      console.log('[Far & Wide Debug] SortableTable initialized with AJAX debugging');

      // Add a test button to manually test AJAX with new method
      setTimeout(function() {
        var testButton = new Element('button', {style: 'margin: 10px; padding: 5px; background: #ff0000; color: white;'});
        testButton.innerHTML = 'Test AJAX Save (New Method)';
        testButton.observe('click', function() {
          console.log('[Far & Wide Debug] Manual AJAX test with new method...');
          new Ajax.Request('/webadmin/navigation_menus/save_field/2.json', {
            method: 'post',
            postBody: 'data[NavigationMenu][order]=0',
            onSuccess: function(response) {
              console.log('[Far & Wide Debug] Manual AJAX Success:', response.responseText);
              alert('AJAX Success: ' + response.responseText);
            },
            onFailure: function(response) {
              console.error('[Far & Wide Debug] Manual AJAX Failed:', response.status, response.responseText);
              alert('AJAX Failed: ' + response.status + ' - ' + response.responseText);
            }
          });
        });
        $('farWideTable').insert({before: testButton});
      }, 1000);
    ", array('inline' => false)); ?>

  <?php else: ?>
    <p>No Far & Wide items found.</p>
  <?php endif; ?>
</div>
