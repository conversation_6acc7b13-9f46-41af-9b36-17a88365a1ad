<div id="farWideIndex" class="index">
  <div class="title clearfix">
    <h2><?php __('Far & Wide');?></h2>
    <?php echo $this->element('webadmin_actions'); ?>
  </div>

  <?php $webAdmin->sessionFlash(); ?>



  <?php if (!empty($navigationMenus)): ?>
    <?php if (!empty($breadcrumbs)): ?>
      <?php echo $this->element('webadmin_breadcrumb'); ?>
    <?php endif; ?>

    <div class="table">
      <table cellpadding="0" cellspacing="0" id="farWideTable">
        <thead>
          <tr>
            <th class="reorder"></th>
            <th>Name</th>
            <th>URL</th>
            <th>Menu Type</th>
            <th>Published</th>
            <th>Created</th>
            <th>Modified</th>
            <th class="actions"><?php __('Actions'); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php
          // Render menu items hierarchically
          function renderMenuItems($items, $html, $level = 0) {
              $output = '';
              $i = 0;

              foreach ($items as $item) {
                  $class = '';
                  if ($i++ % 2 == 0) {
                      $class = ' class="altrow"';
                  }

                  $indent = str_repeat('&nbsp;&nbsp;&nbsp;', $level);
                  $published = $item['NavigationMenu']['published'] ? 'Yes' : 'No';

                  $output .= '<tr' . $class . ' id="row-' . $item['NavigationMenu']['id'] . '">';
                  $output .= '<td class="reorder">';
                  $output .= '<span class="drag">Drag</span>';
                  $output .= '</td>';
                  $output .= '<td>' . $indent . h($item['NavigationMenu']['name']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['url']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['menu_type']) . '</td>';
                  $output .= '<td>' . $published . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['created']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['modified']) . '</td>';
                  $output .= '<td class="actions">';
                  $output .= $html->link(__('Edit', true), array('action' => 'webadmin_edit', $item['NavigationMenu']['id']));
                  $output .= ' ';
                  $output .= $html->link(__('Delete', true), array('action' => 'webadmin_delete', $item['NavigationMenu']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $item['NavigationMenu']['id']));
                  $output .= ' ';
                  $publishText = $item['NavigationMenu']['published'] ? 'Unpublish' : 'Publish';
                  $output .= $html->link($publishText, array('action' => 'webadmin_toggle_field', 'published', $item['NavigationMenu']['id']));
                  $output .= '</td>';
                  $output .= '</tr>';

                  // Render children
                  if (!empty($item['children'])) {
                      $output .= renderMenuItems($item['children'], $html, $level + 1);
                  }
              }

              return $output;
          }

          echo renderMenuItems($navigationMenus, $html, 0);
          ?>
        </tbody>
      </table>
    </div>
    <?php echo $javascript->codeBlock("
      document.observe('dom:loaded', function() {
        console.log('[Far & Wide Debug] DOM loaded, initializing SortableTable...');

        // Check if the table element exists
        var tableElement = $('farWideTable');
        console.log('[Far & Wide Debug] Table element found:', tableElement);

        if (!tableElement) {
          console.error('[Far & Wide Debug] Table element farWideTable not found!');
          return;
        }

        console.log('[Far & Wide Debug] Table rows:', tableElement.select('tr[id^=\"row-\"]'));

        try {
          var farWideSortable = new Webadmin.SortableTable('farWideTable', {
            controller: 'navigation_menus',
            model: 'NavigationMenu'
          });
          console.log('[Far & Wide Debug] SortableTable created successfully:', farWideSortable);

          // Test drag functionality after a short delay
          setTimeout(function() {
            console.log('[Far & Wide Debug] Testing drag after delay...');
            console.log('[Far & Wide Debug] Sortable serialize:', farWideSortable.sortable.serialize());

            // Try to manually trigger a drag event to test
            var firstRow = tableElement.select('tr[id^=\"row-\"]')[0];
            if (firstRow) {
              console.log('[Far & Wide Debug] First row for testing:', firstRow);
              console.log('[Far & Wide Debug] Row ID:', firstRow.identify());
            }
          }, 2000);

        } catch(e) {
          console.error('[Far & Wide Debug] Failed to create SortableTable:', e);
        }
      });
    ", array('inline' => false)); ?>

  <?php else: ?>
    <p>No Far & Wide items found.</p>
  <?php endif; ?>
</div>
