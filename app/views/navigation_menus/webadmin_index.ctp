<div id="farWideIndex" class="index">
  <div class="title clearfix">
    <h2><?php __('Far & Wide');?></h2>
    <?php echo $this->element('webadmin_actions'); ?>
  </div>

  <?php $webAdmin->sessionFlash(); ?>



  <?php if (!empty($navigationMenus)): ?>
    <?php if (!empty($breadcrumbs)): ?>
      <?php echo $this->element('webadmin_breadcrumb'); ?>
    <?php endif; ?>

    <div class="table">
      <table cellpadding="0" cellspacing="0" id="farWideTable">
        <thead>
          <tr>
            <th class="reorder"></th>
            <th>Name</th>
            <th>URL</th>
            <th>Menu Type</th>
            <th>Published</th>
            <th>Created</th>
            <th>Modified</th>
            <th class="actions"><?php __('Actions'); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php
          // Render menu items hierarchically
          function renderMenuItems($items, $html, $level = 0) {
              $output = '';
              $i = 0;

              foreach ($items as $item) {
                  $class = '';
                  if ($i++ % 2 == 0) {
                      $class = ' class="altrow"';
                  }

                  $indent = str_repeat('&nbsp;&nbsp;&nbsp;', $level);
                  $published = $item['NavigationMenu']['published'] ? 'Yes' : 'No';

                  $output .= '<tr' . $class . ' id="row-' . $item['NavigationMenu']['id'] . '">';
                  $output .= '<td class="reorder">';
                  $output .= '<span class="drag">Drag</span>';
                  $output .= '</td>';
                  $output .= '<td>' . $indent . h($item['NavigationMenu']['name']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['url']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['menu_type']) . '</td>';
                  $output .= '<td>' . $published . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['created']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['modified']) . '</td>';
                  $output .= '<td class="actions">';
                  $output .= $html->link(__('Edit', true), array('action' => 'webadmin_edit', $item['NavigationMenu']['id']));
                  $output .= ' ';
                  $output .= $html->link(__('Delete', true), array('action' => 'webadmin_delete', $item['NavigationMenu']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $item['NavigationMenu']['id']));
                  $output .= ' ';
                  $publishText = $item['NavigationMenu']['published'] ? 'Unpublish' : 'Publish';
                  $output .= $html->link($publishText, array('action' => 'webadmin_edit', $item['NavigationMenu']['id'], '?' => array('toggle' => 'published')));
                  $output .= '</td>';
                  $output .= '</tr>';

                  // Render children
                  if (!empty($item['children'])) {
                      $output .= renderMenuItems($item['children'], $html, $level + 1);
                  }
              }

              return $output;
          }

          echo renderMenuItems($navigationMenus, $html, 0);
          ?>
        </tbody>
      </table>
    </div>
    <?php echo $javascript->codeBlock("
      console.log('[NavigationMenus Debug] Initializing SortableTable...');
      var sortableTable = new Webadmin.SortableTable('farWideTable', {
        controller: 'navigation_menus',
        model: 'NavigationMenu'
      });

      // Add debugging to multiple methods
      console.log('[NavigationMenus Debug] SortableTable object:', sortableTable);
      console.log('[NavigationMenus Debug] Available methods:', Object.getOwnPropertyNames(sortableTable));

      // Check which implementation we're using and override the correct method
      if (sortableTable.onDragEnd) {
        console.log('[NavigationMenus Debug] Using webadmin-ui implementation (onDragEnd)');
        var originalOnDragEnd = sortableTable.onDragEnd;
        sortableTable.onDragEnd = function(draggable, droppable, e) {
          console.log('[NavigationMenus Debug] onDragEnd called');
          console.log('[NavigationMenus Debug] Draggable:', draggable);
          console.log('[NavigationMenus Debug] Proxy ID:', this.proxy.retrieve('webadmin:sortabletable:id'));

          var id = this.proxy.retrieve('webadmin:sortabletable:id');
          var value = this.serialize().indexOf(id);
          console.log('[NavigationMenus Debug] New position:', value, 'ID:', id);

          var replacements = Object.extend(this.options, {
            value : value,
            id : id.replace(/\\D/g, '')
          });

          var url = this.url.evaluate(replacements);
          var postBody = this.postBody.evaluate(replacements);

          console.log('[NavigationMenus Debug] Making AJAX request to:', url);
          console.log('[NavigationMenus Debug] POST data:', postBody);

          new Ajax.Request(url, {
            method : 'post',
            postBody : postBody,
            onSuccess : function(response) {
              console.log('[NavigationMenus Debug] AJAX Success:', response.responseText);
            },
            onFailure : function(response) {
              console.error('[NavigationMenus Debug] AJAX Failed:', response.status, response.responseText);
            },
            onException : function(response, exception) {
              console.error('[NavigationMenus Debug] AJAX Exception:', exception);
            }
          });

          return originalOnDragEnd.call(this, draggable, droppable, e);
        };
      } else if (sortableTable.onEnd) {
        console.log('[NavigationMenus Debug] Using application.js implementation (onEnd)');

        // Wait for the UI.SortableTable to be initialized, then override its callbacks
        setTimeout(function() {
          if (sortableTable.sortable && sortableTable.sortable.options) {
            console.log('[NavigationMenus Debug] Overriding UI.SortableTable callbacks...');

            // Store original callbacks
            var originalOnDrag = sortableTable.sortable.options.onDrag;
            var originalOnEnd = sortableTable.sortable.options.onEnd;

            // Override the UI.SortableTable's onDrag callback
            sortableTable.sortable.options.onDrag = function(row) {
              console.log('[NavigationMenus Debug] UI.SortableTable onDrag called for row:', row.identify());
              sortableTable.index = sortableTable.sortable.serialize().indexOf(row.identify());
              console.log('[NavigationMenus Debug] Starting position:', sortableTable.index);
              if (originalOnDrag) return originalOnDrag.call(this, row);
            };

            // Override the UI.SortableTable's onEnd callback
            sortableTable.sortable.options.onEnd = function(row) {
              console.log('[NavigationMenus Debug] UI.SortableTable onEnd called for row:', row.identify());

              var value = sortableTable.sortable.serialize().indexOf(row.identify());
              console.log('[NavigationMenus Debug] New position:', value, 'Old position:', sortableTable.index);

              if(value != sortableTable.index) {
                var replacements = Object.extend(sortableTable.options, {
                  value : value,
                  id : row.identify().toRecordId()
                });

                // Use webadmin_save_field action (the correct action for drag reordering)
                var customReplacements = Object.extend(sortableTable.options, {
                  value : value,
                  id : row.identify().toRecordId(),
                  action: 'webadmin_save_field'
                });
                var url = '/webadmin/navigation_menus/save_field/' + row.identify().toRecordId();
                var postBody = 'data[NavigationMenu][order]=' + value;

                console.log('[NavigationMenus Debug] Making AJAX request to:', url);
                console.log('[NavigationMenus Debug] POST data:', postBody);

                new Ajax.Request(url, {
                  method : 'post',
                  postBody : postBody,
                  onSuccess : function(response) {
                    console.log('[NavigationMenus Debug] AJAX Success:', response.responseText);
                  },
                  onFailure : function(response) {
                    console.error('[NavigationMenus Debug] AJAX Failed:', response.status, response.responseText);
                  },
                  onException : function(response, exception) {
                    console.error('[NavigationMenus Debug] AJAX Exception:', exception);
                  }
                });
              } else {
                console.log('[NavigationMenus Debug] No position change, skipping AJAX');
              }
              sortableTable.index = null;

              if (originalOnEnd) return originalOnEnd.call(this, row);
            };

            console.log('[NavigationMenus Debug] UI.SortableTable callbacks overridden successfully');
          } else {
            console.log('[NavigationMenus Debug] UI.SortableTable not ready for callback override');
          }
        }, 100);
      } else {
        console.log('[NavigationMenus Debug] No drag end method found!');
      }

      // Also check if sortable is properly initialized
      setTimeout(function() {
        console.log('[NavigationMenus Debug] Checking sortable after delay...');
        if (sortableTable.element) {
          console.log('[NavigationMenus Debug] Element:', sortableTable.element);
          console.log('[NavigationMenus Debug] Element has drag handles:', sortableTable.element.select('span.drag').length);
        }
        if (sortableTable.sortable) {
          console.log('[NavigationMenus Debug] UI.SortableTable:', sortableTable.sortable);
          console.log('[NavigationMenus Debug] UI.SortableTable options:', sortableTable.sortable.options);

          // Test if we can manually trigger the drag events
          var firstRow = sortableTable.sortable.element.down('tbody tr');
          if (firstRow) {
            console.log('[NavigationMenus Debug] First row:', firstRow);
            console.log('[NavigationMenus Debug] First row drag handle:', firstRow.down('span.drag'));

            // Try to manually trigger a mousedown event on the drag handle
            var dragHandle = firstRow.down('span.drag');
            if (dragHandle) {
              console.log('[NavigationMenus Debug] Adding manual click listener to test drag handle...');
              dragHandle.observe('click', function(e) {
                console.log('[NavigationMenus Debug] Drag handle clicked!');
              });

            // Add test buttons to debug the issue
            var testButton1 = new Element('button', {style: 'margin: 10px; padding: 5px; background: #0066cc; color: white;'});
            testButton1.innerHTML = 'Test save_field AJAX';
            testButton1.observe('click', function() {
              console.log('[NavigationMenus Debug] save_field AJAX test...');
              new Ajax.Request('/webadmin/navigation_menus/save_field/5', {
                method: 'post',
                postBody: 'data[NavigationMenu][order]=99',
                requestHeaders: {'X-Requested-With': 'XMLHttpRequest'},
                onSuccess: function(response) {
                  console.log('[NavigationMenus Debug] save_field AJAX Success:', response.responseText);
                  alert('save_field Success: ' + response.responseText);
                },
                onFailure: function(response) {
                  console.error('[NavigationMenus Debug] save_field AJAX Failed:', response.status, response.responseText);
                  alert('save_field Failed: ' + response.status + ' - ' + response.responseText);
                }
              });
            });

            var testButton2 = new Element('button', {style: 'margin: 10px; padding: 5px; background: #cc6600; color: white;'});
            testButton2.innerHTML = 'Test GET AJAX';
            testButton2.observe('click', function() {
              console.log('[NavigationMenus Debug] GET AJAX test...');
              new Ajax.Request('/webadmin/navigation_menus/edit/5', {
                method: 'get',
                requestHeaders: {'X-Requested-With': 'XMLHttpRequest'},
                onSuccess: function(response) {
                  console.log('[NavigationMenus Debug] GET AJAX Success:', response.responseText.length + ' chars');
                  alert('GET Success: ' + response.responseText.length + ' characters received');
                },
                onFailure: function(response) {
                  console.error('[NavigationMenus Debug] GET AJAX Failed:', response.status, response.responseText);
                  alert('GET Failed: ' + response.status);
                }
              });
            });

            $('farWideTable').insert({before: testButton1});
            $('farWideTable').insert({before: testButton2});
            }
          }
        }
      }, 1000);

      console.log('[NavigationMenus Debug] SortableTable initialized');
    ", array('inline' => false)); ?>

  <?php else: ?>
    <p>No Far & Wide items found.</p>
  <?php endif; ?>
</div>
