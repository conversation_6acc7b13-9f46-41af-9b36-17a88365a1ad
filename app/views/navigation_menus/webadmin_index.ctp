<div id="farWideIndex" class="index">
  <div class="title clearfix">
    <h2><?php __('Far & Wide');?></h2>
    <?php echo $this->element('webadmin_actions'); ?>
  </div>

  <?php $webAdmin->sessionFlash(); ?>



  <?php if (!empty($navigationMenus)): ?>
    <?php if (!empty($breadcrumbs)): ?>
      <?php echo $this->element('webadmin_breadcrumb'); ?>
    <?php endif; ?>

    <div class="table">
      <table cellpadding="0" cellspacing="0" id="farWideTable">
        <thead>
          <tr>
            <th class="reorder"></th>
            <th>Name</th>
            <th>URL</th>
            <th>Menu Type</th>
            <th>Published</th>
            <th>Created</th>
            <th>Modified</th>
            <th class="actions"><?php __('Actions'); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php
          // Render menu items hierarchically
          function renderMenuItems($items, $html, $level = 0) {
              $output = '';
              $i = 0;

              foreach ($items as $item) {
                  $class = '';
                  if ($i++ % 2 == 0) {
                      $class = ' class="altrow"';
                  }

                  $indent = str_repeat('&nbsp;&nbsp;&nbsp;', $level);
                  $published = $item['NavigationMenu']['published'] ? 'Yes' : 'No';

                  $output .= '<tr' . $class . ' id="row-' . $item['NavigationMenu']['id'] . '">';
                  $output .= '<td class="reorder">';
                  $output .= '<span class="drag">Drag</span>';
                  $output .= '</td>';
                  $output .= '<td>' . $indent . h($item['NavigationMenu']['name']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['url']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['menu_type']) . '</td>';
                  $output .= '<td>' . $published . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['created']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['modified']) . '</td>';
                  $output .= '<td class="actions">';
                  $output .= $html->link(__('Edit', true), array('action' => 'webadmin_edit', $item['NavigationMenu']['id']));
                  $output .= ' ';
                  $output .= $html->link(__('Delete', true), array('action' => 'webadmin_delete', $item['NavigationMenu']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $item['NavigationMenu']['id']));
                  $output .= ' ';
                  $publishText = $item['NavigationMenu']['published'] ? 'Unpublish' : 'Publish';
                  $output .= $html->link($publishText, array('action' => 'webadmin_edit', $item['NavigationMenu']['id'], '?' => array('toggle' => 'published')));
                  $output .= '</td>';
                  $output .= '</tr>';

                  // Render children
                  if (!empty($item['children'])) {
                      $output .= renderMenuItems($item['children'], $html, $level + 1);
                  }
              }

              return $output;
          }

          echo renderMenuItems($navigationMenus, $html, 0);
          ?>
        </tbody>
      </table>
    </div>
    <?php echo $javascript->codeBlock("
      console.log('[NavigationMenus Debug] Initializing SortableTable...');
      var sortableTable = new Webadmin.SortableTable('farWideTable', {
        controller: 'navigation_menus',
        model: 'NavigationMenu'
      });

      // Override onEnd to add debugging
      var originalOnEnd = sortableTable.onEnd.bind(sortableTable);
      sortableTable.onEnd = function(row) {
        console.log('[NavigationMenus Debug] Drag ended for row:', row.identify());

        var value = this.sortable.serialize().indexOf(row.identify());
        console.log('[NavigationMenus Debug] New position:', value, 'Old position:', this.index);

        if(value != this.index) {
          var replacements = Object.extend(this.options, {
            value : value,
            id : row.identify().toRecordId()
          });

          var url = this.url.evaluate(replacements);
          var postBody = this.postBody.evaluate(replacements);

          console.log('[NavigationMenus Debug] Making AJAX request to:', url);
          console.log('[NavigationMenus Debug] POST data:', postBody);

          new Ajax.Request(url, {
            method : 'post',
            postBody : postBody,
            onSuccess : function(response) {
              console.log('[NavigationMenus Debug] AJAX Success:', response.responseText);
            },
            onFailure : function(response) {
              console.error('[NavigationMenus Debug] AJAX Failed:', response.status, response.responseText);
            },
            onException : function(response, exception) {
              console.error('[NavigationMenus Debug] AJAX Exception:', exception);
            }
          });
        } else {
          console.log('[NavigationMenus Debug] No position change, skipping AJAX');
        }
        this.index = null;
      };

      console.log('[NavigationMenus Debug] SortableTable initialized');
    ", array('inline' => false)); ?>

  <?php else: ?>
    <p>No Far & Wide items found.</p>
  <?php endif; ?>
</div>
