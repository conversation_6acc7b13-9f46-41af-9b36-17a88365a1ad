<div id="farWide" class="edit">
  <div class="title clearfix">
    <h2><?php __('Edit Far & Wide Item');?></h2>
    <?php echo $this->element('webadmin_actions'); ?>
  </div>
  <?php $webAdmin->sessionFlash(); ?>
  <?php echo $this->element('webadmin_breadcrumb'); ?>
  <?php echo $this->element('webadmin_related_tabs_labels'); ?>

  <div class="form">
    <div class="wrapper">
      <div class="subwrapper">
        <?php echo $webAdmin->formCreate('NavigationMenu');?>
          <fieldset>
            <legend><?php __('Far & Wide Item Details'); ?></legend>
            <?php
              echo $webAdmin->formInput('id');

              echo $webAdmin->formInput('name', array(
                'label' => 'Name',
                'type' => 'text',
                'div' => 'input text required'
              ));

              echo $webAdmin->formInput('url', array(
                'label' => 'URL',
                'type' => 'text',
                'div' => 'input text required',
                'after' => '<div class="input-help">Enter the full URL path (e.g., /far_wide/south_america)</div>'
              ));

              echo $webAdmin->formInput('page_id', array(
                'label' => 'Content Page',
                'type' => 'select',
                'options' => $pageOptions,
                'div' => 'input select',
                'after' => '<div class="input-help">Optional: Select a page to use for content instead of the URL</div>'
              ));

              echo $webAdmin->formInput('menu_type', array(
                'label' => 'Menu Type',
                'type' => 'select',
                'options' => $menuTypes,
                'div' => 'input select required'
              ));

              echo $webAdmin->formInput('parent_id', array(
                'label' => 'Parent Item',
                'type' => 'select',
                'options' => $parentOptions,
                'div' => 'input select',
                'after' => '<div class="input-help">Select a parent item to create a sub-menu item</div>'
              ));

              echo $webAdmin->formInput('order', array(
                'label' => 'Order',
                'type' => 'text',
                'div' => 'input text',
                'after' => '<div class="input-help">Optional: Specify display order (lower numbers appear first)</div>'
              ));

              echo $webAdmin->formInput('published', array(
                'label' => 'Published',
                'type' => 'checkbox',
                'div' => 'input checkbox'
              ));
            ?>
          </fieldset>
        <?php
        echo $webAdmin->formSubmit(__('Save', true), array('name' => 'submit'));
        echo $webAdmin->formSubmit(__('Save and Go Back', true), array('name' => 'submit'));
        echo $webAdmin->formEnd();
        ?>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Update parent options when menu type changes
    $('#NavigationMenuMenuType').change(function() {
        var menuType = $(this).val();
        // You could add AJAX here to update parent options based on menu type
        // For now, we'll keep it simple
    });
});
</script>
