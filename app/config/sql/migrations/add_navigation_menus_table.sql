-- Migration: Populate navigation_menus table for Far & Wide navigation
-- Date: 2025-07-15
-- Note: Table already exists in schema, this just populates it with Far & Wide data

-- Clear existing Far & Wide menu items
DELETE FROM `navigation_menus` WHERE `menu_type` = 'far_wide';

-- Insert seed data for Far & Wide menu with hierarchical structure
-- South America and Worldwide are top-level items, Cruises and Escorted Tours are children of Worldwide
INSERT INTO `navigation_menus` (`id`, `parent_id`, `lft`, `rght`, `child_count`, `direct_child_count`, `name`, `url`, `menu_type`, `order`, `published`, `created`, `modified`) VALUES
(2, NULL, 1, 2, 0, 0, 'South America', '/far_wide/south_america', 'far_wide', 1, 1, NOW(), NOW()),
(3, NULL, 3, 8, 2, 2, 'Worldwide', '/far_wide/worldwide', 'far_wide', 2, 1, NOW(), NOW()),
(4, 3, 4, 5, 0, 0, 'Cruises', '/far_wide/worldwide/cruises', 'far_wide', 1, 1, NOW(), NOW()),
(5, 3, 6, 7, 0, 0, 'Escorted Tours', '/far_wide/worldwide/escorted_tours', 'far_wide', 2, 1, NOW(), NOW());
