<?php

class NavigationMenusController extends AppController {

    var $name = 'NavigationMenus';
    var $helpers = array('Tree', 'WebAdmin');

    function beforeFilter() {
        parent::beforeFilter();

        // Explicitly set webadmin layout for all actions
        $this->layout = 'webadmin';

        if (in_array($this->action, array('webadmin_add', 'webadmin_edit'))) {
            $this->_setMenuTypes();
            $this->_setParentOptions();
            $this->_setPageOptions();
        }
    }

    function webadmin_index() {
        // Set up basic variables that the view expects
        $this->set(array(
            'singularVar' => 'navigationMenu',
            'pluralVar' => 'navigationMenus',
            'singularHumanName' => 'Far & Wide Item',
            'pluralHumanName' => 'Far & Wide',
            'modelClass' => 'NavigationMenu'
        ));

        // Get menu type filter
        $menuType = isset($this->params['named']['menu_type']) ? $this->params['named']['menu_type'] : 'far_wide';

        // Get filtered navigation menus
        $conditions = array();
        if ($menuType && $menuType != 'all') {
            $conditions['NavigationMenu.menu_type'] = $menuType;
        }

        $navigationMenus = $this->NavigationMenu->find('threaded', array(
            'conditions' => $conditions,
            'order' => 'NavigationMenu.lft ASC'
        ));

        $menuTypes = $this->NavigationMenu->getMenuTypes();
        $this->set(compact('navigationMenus', 'menuTypes', 'menuType'));

        $this->pageTitle = 'Far & Wide';
    }

    function webadmin_add() {
        // Set up basic variables that the view expects
        $this->set(array(
            'singularVar' => 'navigationMenu',
            'pluralVar' => 'navigationMenus',
            'singularHumanName' => 'Far & Wide Item',
            'pluralHumanName' => 'Far & Wide',
            'modelClass' => 'NavigationMenu',
            'breadcrumbs' => false, // No breadcrumbs for top level
            'parents' => array(), // Empty parents list
            'menuTypes' => $this->NavigationMenu->getMenuTypes(),
            'associations' => array(
                'belongsTo' => array(),
                'hasOne' => array(),
                'hasMany' => array(),
                'hasAndBelongsToMany' => array()
            )
        ));

        if (!empty($this->data)) {
            $this->NavigationMenu->create();
            if ($this->NavigationMenu->save($this->data)) {
                $this->Session->setFlash(__('The Far & Wide item has been saved', true));

                if (isset($this->params['form']['submit']) && $this->params['form']['submit'] == __('Save and Add Another', true)) {
                    $this->redirect(array('action' => 'webadmin_add'));
                } else {
                    $this->redirect(array('action' => 'webadmin_index'));
                }
            } else {
                $this->Session->setFlash(__('The Far & Wide item could not be saved. Please, try again.', true));
            }
        }

        $this->pageTitle = 'Add Far & Wide Item';
    }

    function webadmin_edit($id = null) {
        // Check if this is an AJAX request for drag reordering
        if ($this->RequestHandler->isAjax() && !empty($this->data)) {
            error_log('[Far & Wide Debug] AJAX edit request for drag reordering');
            return $this->_handleAjaxSaveField($id);
        }

        // Set up basic variables that the view expects
        $this->set(array(
            'singularVar' => 'navigationMenu',
            'pluralVar' => 'navigationMenus',
            'singularHumanName' => 'Far & Wide Item',
            'pluralHumanName' => 'Far & Wide',
            'modelClass' => 'NavigationMenu',
            'breadcrumbs' => false, // No breadcrumbs for top level
            'parents' => array(), // Empty parents list
            'menuTypes' => $this->NavigationMenu->getMenuTypes(),
            'associations' => array(
                'belongsTo' => array(),
                'hasOne' => array(),
                'hasMany' => array(),
                'hasAndBelongsToMany' => array()
            )
        ));

        if (!$id && empty($this->data)) {
            $this->Session->setFlash(__('Invalid Far & Wide item', true));
            $this->redirect(array('action' => 'webadmin_index'));
        }

        if (!empty($this->data)) {
            if ($this->NavigationMenu->save($this->data)) {
                $this->Session->setFlash(__('The Far & Wide item has been saved', true));

                if (isset($this->params['form']['submit']) && $this->params['form']['submit'] == __('Save and Go Back', true)) {
                    $this->redirect(array('action' => 'webadmin_index'));
                } else {
                    $this->redirect(array('action' => 'webadmin_edit', $id));
                }
            } else {
                $this->Session->setFlash(__('The Far & Wide item could not be saved. Please, try again.', true));
            }
        }

        if (empty($this->data)) {
            $this->data = $this->NavigationMenu->read(null, $id);
        }

        $this->pageTitle = 'Edit Far & Wide Item';
    }

    function _handleAjaxSaveField($id = null) {
        error_log('[Far & Wide Debug] _handleAjaxSaveField called with ID: ' . $id);
        error_log('[Far & Wide Debug] POST data: ' . print_r($this->data, true));

        if (!$id) {
            error_log('[Far & Wide Debug] No ID provided');
            $this->cakeError('error404');
            return;
        }

        // Find the item
        $item = $this->NavigationMenu->findById($id);
        if (!$item) {
            error_log('[Far & Wide Debug] Item not found');
            $this->cakeError('error404');
            return;
        }

        // Get the field and value from POST data
        if (empty($this->data) || !isset($this->data['NavigationMenu']) || empty($this->data['NavigationMenu'])) {
            error_log('[Far & Wide Debug] No POST data');
            $this->cakeError('error404');
            return;
        }

        $field = key($this->data['NavigationMenu']);
        $value = $this->data['NavigationMenu'][$field];

        error_log('[Far & Wide Debug] Saving field: ' . $field . ' = ' . $value);

        // Validate field (only allow 'order' field for drag reordering)
        if ($field !== 'order') {
            error_log('[Far & Wide Debug] Invalid field: ' . $field);
            $this->cakeError('error404');
            return;
        }

        // Save the field
        $this->NavigationMenu->id = $id;
        if ($this->NavigationMenu->saveField($field, $value)) {
            error_log('[Far & Wide Debug] Save successful');
            // Success - return minimal response for AJAX
            $this->autoRender = false;
            echo '1'; // Success indicator
        } else {
            error_log('[Far & Wide Debug] Save failed');
            // Failure
            $this->cakeError('error404');
        }
    }

    function webadmin_delete($id = null) {
        if (!$id) {
            $this->Session->setFlash(__('Invalid id for Far & Wide item', true));
            $this->redirect(array('action' => 'webadmin_index'));
        }

        if ($this->NavigationMenu->delete($id)) {
            $this->Session->setFlash(__('Far & Wide item deleted', true));
        } else {
            $this->Session->setFlash(__('Far & Wide item was not deleted', true));
        }

        $this->redirect(array('action' => 'webadmin_index'));
    }

    function webadmin_move_up($id = null) {
        if (!$id) {
            $this->Session->setFlash(__('Invalid id for Far & Wide item', true));
            $this->redirect(array('action' => 'webadmin_index'));
        }

        if ($this->NavigationMenu->moveup($id)) {
            $this->Session->setFlash(__('Far & Wide item moved up', true));
        } else {
            $this->Session->setFlash(__('Far & Wide item could not be moved up', true));
        }

        $this->redirect(array('action' => 'webadmin_index'));
    }

    function webadmin_move_down($id = null) {
        if (!$id) {
            $this->Session->setFlash(__('Invalid id for Far & Wide item', true));
            $this->redirect(array('action' => 'webadmin_index'));
        }

        if ($this->NavigationMenu->movedown($id)) {
            $this->Session->setFlash(__('Far & Wide item moved down', true));
        } else {
            $this->Session->setFlash(__('Far & Wide item could not be moved down', true));
        }

        $this->redirect(array('action' => 'webadmin_index'));
    }



    function webadmin_save_field($id = null) {
        // Debug: Log that we reached this method
        error_log('[Far & Wide Debug] webadmin_save_field method called with ID: ' . $id);
        error_log('[Far & Wide Debug] Is AJAX: ' . ($this->RequestHandler->isAjax() ? 'yes' : 'no'));
        error_log('[Far & Wide Debug] POST data: ' . print_r($this->data, true));

        // Handle AJAX request for drag reordering
        if (!$this->RequestHandler->isAjax()) {
            error_log('[Far & Wide Debug] Not AJAX, redirecting');
            $this->Session->setFlash(__('Invalid request', true));
            $this->redirect(array('action' => 'webadmin_index'));
            return;
        }

        if (!$id) {
            $this->cakeError('error404');
            return;
        }

        // Find the item
        $item = $this->NavigationMenu->findById($id);
        if (!$item) {
            $this->cakeError('error404');
            return;
        }

        // Get the field and value from POST data
        if (empty($this->data) || !isset($this->data['NavigationMenu']) || empty($this->data['NavigationMenu'])) {
            $this->cakeError('error404');
            return;
        }

        $field = key($this->data['NavigationMenu']);
        $value = $this->data['NavigationMenu'][$field];

        // Validate field (only allow 'order' field for drag reordering)
        if ($field !== 'order') {
            $this->cakeError('error404');
            return;
        }

        // Save the field
        $this->NavigationMenu->id = $id;
        if ($this->NavigationMenu->saveField($field, $value)) {
            // Success - return minimal response for AJAX
            $this->autoRender = false;
            echo '1'; // Success indicator
        } else {
            // Failure
            $this->cakeError('error404');
        }
    }

    function _setMenuTypes() {
        $menuTypes = $this->NavigationMenu->getMenuTypes();
        $this->set(compact('menuTypes'));
    }

    function _setParentOptions() {
        $menuType = 'far_wide'; // Default to far_wide for now
        if (!empty($this->data['NavigationMenu']['menu_type'])) {
            $menuType = $this->data['NavigationMenu']['menu_type'];
        }

        $parentOptions = $this->NavigationMenu->generatetreelist(
            array('NavigationMenu.menu_type' => $menuType),
            null, null, '   '  // Use regular spaces instead of &nbsp;
        );
        $parentOptions = array(0 => 'No Parent') + $parentOptions;
        $this->set(compact('parentOptions'));
    }

    function _setPageOptions() {
        $pageOptions = array('' => 'None (Use URL only)');
        $pages = ClassRegistry::init('Page')->find('all', array(
            'fields' => array('Page.id', 'Page.meta_title'),
            'conditions' => array('Page.published' => 1),
            'order' => 'Page.id ASC'
        ));

        // Format as "ID - Page Title"
        foreach ($pages as $page) {
            $pageOptions[$page['Page']['id']] = $page['Page']['id'] . ' - ' . $page['Page']['meta_title'];
        }

        $this->set(compact('pageOptions'));
    }
}

?>
